# Resource object code (Python 3)
# Created by: object code
# Created by: The Resource Compiler for Qt version 6.9.1
# WARNING! All changes made in this file will be lost!

from PySide6 import QtCore

qt_resource_data = b"\
\x00\x00\x02\x82\
\x89\
PNG\x0d\x0a\x1a\x0a\x00\x00\x00\x0dIHDR\x00\
\x00\x00\x18\x00\x00\x00\x18\x08\x06\x00\x00\x00\xe0w=\xf8\
\x00\x00\x00\x01sRGB\x00\xae\xce\x1c\xe9\x00\x00\x02\
<IDATHK\xb5\x95\xbd\x8b\xd4@\x18\xc6\x9f\
7$\xf8\x05\xa2\x8dl\x96\xbc\xb3\xbb\x1e(v\x82\x16\
\xca\x89kc\xa3`\xa7\x85\x16ZY)\xe8_ \xf8\
\x0fh\xa1\x85\x9d\x16^ag\xa1\x9dx\xca\x89Wx\
\xd8\xaa\xa0Kf\x02\xbbp\x82\x1f\xe0\x09n\xd8\x919\
\x92e2\x97d#\xb2\xa9\x86\xf9x\x9f\xf7}f\xde\
_\x08s\xfeh\xce\xf11S`4\x1a\xed\x1f\x8f\xc7\
\x8f\x01\x1cs\x92Y\x0d\x82\xe0R\xab\xd5\xfaR\x97d\
\xa5\xc0p8\xec\xa7i\xba\xdc\xa4B\xdf\xf7O\x85a\
\xf8\xaalo\xa9\x80\x94\xf2!\x11]\xce\x0el\x10\xd1\
\xb9(\x8a^\xd8\x01\xa4\x94'\x89\xe8)\x80=f^\
k\xfdH\x08q\xc5\x15\xd9\x22 \xa5\xbcCD7\xb2\
C}!\xc4\xeb\xba*2\xa1\xcd\xec\xb5\xd6w\x85\x10\
7\xed\xfd\x05\x01\xdb\x16f.\xacI)W\x88h1\
\x0b\xf4\xd3\xf7\xfd\x85v\xbb\xfd5\x0f\xa6\x94\xd2f\xec\
\xdaU\x08\x92o\x9aL&':\x9d\xce\x1b\xeb\xf0o\
\x00\xdb\xb3\xe0c\x22\x0a\xcc8\x8a\xa2mD\xf4\xc7\x8c\
\xe38^\xf4<o\xc5\x8c\xed\xe4\xa6\x02I\x92\x1c\xd0\
Z\x7f\x04\xb0\xc1\xcc\xbb\xf2\xe0\x83\xc1\xe0\xb0\xef\xfb\xef\
\x01\xdcc\xe6\xebf^)u\x15\xc0\x03\x00\xb7\x99\xf9\
\x96\x95\xc8/\x00;\x89\xe8`\x14E\x9f\xcc\xfcT@\
)\xf5\x0e\xc0\x11\x00g\x99\xf9y\x9d\xefJ\xa9>\x80\
e\xad\xf55!\xc4}K\xe0\x0c\x80g\x00\xd6\x98\xf9\
\xa8+\xb0\xe9\xa1\xeb}\x99Pne\xd9^w\xcd\xae\
\xa0\x91\x80u\x99\xfb\xc20\x5cw\x13\xf8/\x01)\xe5\
\x0f\x22\xda\x9d\xa6\xe9\xa1^\xaf\xf7\xa1Iu\xffTA\
\x9d5\xees\xcd\xed\xb3\x05\xde\x1a\xdeh\xadg6W\
\xd5\x03\xb0\x9an\x95\x99\x8f\x17.9\x83\xdag\x00\xdf\
\x99yoY\x90$I.\xa4i\xba\xde\xedv_V\
\xd8\xf3\xcd\xa0#\x08\x82\x85\x1c\x82\xa5\x8dVV\x85R\
j\x87\xe9\x91\xaa\x97f#\xa3\xb4\xd1\xcc\xc1:Td\
\x0dV\xf9\xd2\x1a\xa1\xc2\x04\xb1I\xea\x22\xa3\xcc\x16\x1b\
\x11eD\xad\xc2\xf5\x94\xa8\x99-\xe7\xdd\xee\x8e\xe3\xf8\
\xb4\xe7yO,\x5co!i\xe1\x92\xdd\xec\xe6\xfa\xc3\
\xb1\xc52\x08.e\x9c\xb2\x97\xd6\x88\xe8b\x0e\xb5\xaa\
\xa7;\xf3\x9f\x5c\x07\xbd&ks\x17\xf8\x0bT\x85(\
(\xd3\x0coM\x00\x00\x00\x00IEND\xaeB`\
\x82\
\x00\x00\x00\xd4\
\x89\
PNG\x0d\x0a\x1a\x0a\x00\x00\x00\x0dIHDR\x00\
\x00\x00\x18\x00\x00\x00\x18\x08\x06\x00\x00\x00\xe0w=\xf8\
\x00\x00\x00\x01sRGB\x00\xae\xce\x1c\xe9\x00\x00\x00\
\x8eIDATHK\xed\x96=\x0e\x80 \x0c\x85\xdb\
\x83p>\x17\x07O`\xe2\x09\x1c\x5c<_\xdfA4\
nP\x10\xa9\xa6\x93\xb0\x91\xfe|\xedKS`r>\
\xec\x9c\x9f2\x80\x88\xac\xcc<Y\xc0!\x84\xdbB\x13\
\x03\x80\x85\x88fK\xf2\xcb\xd7\x028\xe2\x00\x00\xc9]\
\x83\xb5\xbd\xe4\xaf;\xe8\x80L\xd2.\xd1\xe3Pt\x89\
\xfe*\xd1\xdbe\xd7\xbc\x8b\xdc\x01\xb5\xf5[\x837w\
\xe0\x06\x10\x91\x9d\x99\x07\xabD\xda?.0{\xea\x00\
lD4~\x81T\x01_\x12\x97b\xdd\x7f\x15'g\
\xc9\xf0\x19\xe9\xf1\x9d\x1a\x00\x00\x00\x00IEND\xae\
B`\x82\
\x00\x00\x02I\
\x89\
PNG\x0d\x0a\x1a\x0a\x00\x00\x00\x0dIHDR\x00\
\x00\x00\x18\x00\x00\x00\x18\x08\x06\x00\x00\x00\xe0w=\xf8\
\x00\x00\x00\x01sRGB\x00\xae\xce\x1c\xe9\x00\x00\x02\
\x03IDATHK\xd5U=\x88\x13A\x14\xfe\xde\
\xa6\xf1,\x14E\x9b\xec|\xb3D-\xc5F\xac\xc4B\
\xe4\xc0Jl\x05\xc1\x22\x88\x88\x0a\x22\x82\x8dx\xa7b\
\xa3\x88\x88\x87\x5cq\xc1\xc2Z,D\x04\x0b\x8bTV\
\x82\x85\xe5\x09\x99\x17\xb6:A\x059\x8b\xdc\x8e\xbb\xb2\
\x1b\x92\xdcn\xeeBL\xe1T\xf3\xf3\xde\xfb\xe6}\xef\
\x9b7\x82\x19\x0f\x99q|l\x0b@U}\xd9EH\
n\xe9\xbf\xa5A\x16\xf8\x9f\x02\xc4q\xbc\xaf^\xaf\xaf\
\x0d\xde\xb8\x00(n<\xba.l\xcb|7e\xa0\xaa\
\xbf\x00\xec\x04\xd06\xc6\x9cT\xd5\xb6\x88\x1c\xcf\x82\x8c\
\x02\x00\xf8\xd0j\xb5\xe6\x9b\xcd\xe6[\x00\xa7\x01\xac\x93\
\xcc|\xfbc\x08@U3\xa3w\x15\x85\xffJ\xf2`\
v\xe6\x9c[\x15\x91\x03evI\x92\x9c\x89\xa2\xe8M\
q6\x0a\xf0\xb7\x98\xde\xfbc\x22r\x01\xc0U\xef\xfd\
Ok\xed\xee\xb2`\x03\xd9>\x02\xf0*\xcd\xe2\xe3`\
\xa6\xd9\xbc\x0f\xe0\x9c[\x16\x91K\x00\xbe\x93\xdcS\x16\
\xd09w\xaeV\xab\xed\x08\xc3\xf0E\xc5\xf9\x0f\x11\xd9\
\x05`\x85\xe4\xc5!\x00U\xfd\x06`o\xeex+\xe5\
\xf2a\x11\xc49wVD^\x0fq+r\xd3\x18\xf3\
\xb8\xd8S\xd5\xf3\x00^\xe6\xeb5\x92\xfb\x87\x00r9\
\xae\xa4\xaal\xe6F\xab$\x0f\x8d\x93i\xaf\xd7\x9bk\
4\x1a\xbfU\xf53\x80#\xb9\xdfS\x92\xd7Kk\x90\
mv:\x9d\xc5 \x08\x16\x00,\x91\xbc\x16\xc7\xf1\x89\
\x8d\x8d\x8dv\x19%\xde\xfb;\xd6\xda\xfb\xaa\xfa,\xab\
W\x92$w\xa3(Z\xacT\xd1 \x80\xf7\xfe\xb9\xb5\
\xf6\xca$\x00\xa9 n\xa4\x82x2N\xa6}\x8a\xbc\
\xf7_\xac\xb5\x87'\xa5HDn\x1bc\x1el\xa2h\
\xb0\xc8\x22r\xd9\x18\xb3<E\x91\xbb$9K\x99.\
\x90\xbcW\xa6\xa2\xd9=\xb4\x9c\xeb\xa9[E\x10\x04G\
\xc30\xfcT)\xd3I\x9b\x9d1f\xbe\xdb\xed\xbe\x07\
p*\x95j\xff\x81U\x02L\xd3\xaeUu\x8e\xe4\xfa\
\xd8wP\xd1\xd4\xfe\xa3\x1f\xad,\x83i\xf6\xb6\xf5'\
O\x03\xf0\x07')%(^\xc1\xa6\x17\x00\x00\x00\x00\
IEND\xaeB`\x82\
\x00\x00\x00\xa1\
\x89\
PNG\x0d\x0a\x1a\x0a\x00\x00\x00\x0dIHDR\x00\
\x00\x00\x18\x00\x00\x00\x18\x08\x06\x00\x00\x00\xe0w=\xf8\
\x00\x00\x00\x01sRGB\x00\xae\xce\x1c\xe9\x00\x00\x00\
[IDATHKcd\xa01`\xa4\xb1\xf9\x0c\
\xa3\x16\x10\x0ca\xfa\x06\xd1\xe3\xc7\x8f\xff\x13t\x12\x11\
\x0adee\xe1\x0eG\xf1\x01\xcd- \xc2q$+\
\xa1o\x1c\x90\xec<\x224\xd07\x0eF#\x19[\x94\
\xd07\x15\xd1<\x0ehn\x01\x11\xc9\x9ad%\xf4\x8d\
\x03\x92\x9dG\x84\x86Q\x1f\x10\x0c$\x00<\xd4\x18\x19\
\xfb\xecx\x03\x00\x00\x00\x00IEND\xaeB`\x82\
\
\x00\x00\x01\xa3\
\x89\
PNG\x0d\x0a\x1a\x0a\x00\x00\x00\x0dIHDR\x00\
\x00\x00\x18\x00\x00\x00\x18\x08\x06\x00\x00\x00\xe0w=\xf8\
\x00\x00\x00\x01sRGB\x00\xae\xce\x1c\xe9\x00\x00\x01\
]IDATHK\xed\x94\xbdJ\xc4@\x14\x85\xef\
\x9d\x04\x0b\xb1\x10\xc4B2\xd7\x94\x16\x0a\xdb[\x89\xcf\
a\xa1\x8d\xdbZ[\x05,\xec\xb4\x15d\xd1'\xf0\x09\
\x84\xad\xb7\x11\xd4\xceF\xc8\x5cR\xd9Z\x04\x99\xb9\x12\
0\x90\x9d\xcd\xe6\x07w+M5\xe4\xcc\x9co\xce\x99\
a\x10\x96\xfc\xe1\x92\xfd\xe1\x8f\x01\x98y \x22BD\
/]\xab\xed\x5cQ\x9a\xa67\x888\xfc1\xbe%\xa2\
\xd3.\x90N\x00c\xcc+\x00\xecU\x0d\x9ds\xe7q\
\x1c_\xb6AZ\x01i\x9af\x88\xb8\xe5\x19\x0d\x9ds\
\x8fJ\xa9'\x22Zo\x824\x02\x8c1\xe2/v\xce\
\xed*\xa5v\x00\xe0\xa1\xd4\x88h\xae\xcf\x5c\xa1\xce<\
\x08\x82\xcd<\xcf\xd7\xc20|\xf7\xc1Z\xeb\x15D\xfc\
\xf2\xff\xcf\x00\x98Y\x8b\x88\xf1'Vw\x99e\xd9\xaa\
\xb5\xf6\xd3\x9f\xa3\x94\xda\x8e\xa2hj\xed\x0c\xa0f\xe7\
\x96\x88\xc2\xba\x9e\xebR\xfau\xb5\x01\x98\x88\xa8\xe9\x10\
\x8d1\x1f\x00\xb01\xef<\x9a\x00\x93\xd1h\xb4\x9f$\
\x89k\x020\xf3\x9d\x88\x1c\xf7\x02 \xe25\x00\x5c\x01\
\xc0\x85\xd6\xfad\xd1\x80#\x00x\x03\x80\x09\x22\xde/\
\x1c\xc0\xccg\x22R$\x80\x02`\xad}\x0e\x82`P\
\x97\xa2\xd4zWT\x9a\x15\x80b\x5c5\xa8\x82\xea\xf4\
^\xb7h)\x00f\x1e\x8b\xc8AYQ\xcf\x04c\x22\
:\x9cJ\xd9\xf6\x1a\xfeVo}M\xff\x01\xdf\xdc\xdf\
\xb5\x19\xa2o\xbd\xb9\x00\x00\x00\x00IEND\xaeB\
`\x82\
"

qt_resource_name = b"\
\x00\x09\
\x0alxC\
\x00r\
\x00e\x00s\x00o\x00u\x00r\x00c\x00e\x00s\
\x00\x05\
\x00o\xa6S\
\x00i\
\x00c\x00o\x00n\x00s\
\x00\x04\
\x00\x06\xa8\x8b\
\x00d\
\x00a\x00r\x00k\
\x00\x08\
\x0c3Z\x87\
\x00h\
\x00e\x00l\x00p\x00.\x00p\x00n\x00g\
\x00\x08\
\x06^Zg\
\x00b\
\x00o\x00o\x00k\x00.\x00p\x00n\x00g\
\x00\x0c\
\x0b\xdf!G\
\x00s\
\x00e\x00t\x00t\x00i\x00n\x00g\x00s\x00.\x00p\x00n\x00g\
\x00\x08\
\x0cXY'\
\x00m\
\x00e\x00n\x00u\x00.\x00p\x00n\x00g\
\x00\x08\
\x068Z\xa7\
\x00h\
\x00o\x00m\x00e\x00.\x00p\x00n\x00g\
"

qt_resource_struct = b"\
\x00\x00\x00\x00\x00\x02\x00\x00\x00\x01\x00\x00\x00\x01\
\x00\x00\x00\x00\x00\x00\x00\x00\
\x00\x00\x00\x00\x00\x02\x00\x00\x00\x01\x00\x00\x00\x02\
\x00\x00\x00\x00\x00\x00\x00\x00\
\x00\x00\x00\x18\x00\x02\x00\x00\x00\x01\x00\x00\x00\x03\
\x00\x00\x00\x00\x00\x00\x00\x00\
\x00\x00\x00(\x00\x02\x00\x00\x00\x05\x00\x00\x00\x04\
\x00\x00\x00\x00\x00\x00\x00\x00\
\x00\x00\x00\x96\x00\x00\x00\x00\x00\x01\x00\x00\x06P\
\x00\x00\x01\x98^R\x22\x0b\
\x00\x00\x00L\x00\x00\x00\x00\x00\x01\x00\x00\x02\x86\
\x00\x00\x01\x98^\x8a=7\
\x00\x00\x00b\x00\x00\x00\x00\x00\x01\x00\x00\x03^\
\x00\x00\x01\x98^s\xcf\xb4\
\x00\x00\x006\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\
\x00\x00\x01\x98^s+\x94\
\x00\x00\x00\x80\x00\x00\x00\x00\x00\x01\x00\x00\x05\xab\
\x00\x00\x01\x98^\xa0\x05+\
"

def qInitResources():
    QtCore.qRegisterResourceData(0x03, qt_resource_struct, qt_resource_name, qt_resource_data)

def qCleanupResources():
    QtCore.qUnregisterResourceData(0x03, qt_resource_struct, qt_resource_name, qt_resource_data)

qInitResources()
