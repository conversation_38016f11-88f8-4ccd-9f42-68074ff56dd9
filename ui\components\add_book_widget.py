"""添加书籍组件"""

from PySide6.QtWidgets import QWidget, QVBoxLayout, QLabel, Q<PERSON>rame
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QPixmap, QColor, QPainter, QFont

from config.settings import BOOKSHELF_CONFIG
from config.styles import BOOK_COVER_STYLE


class AddBookWidget(QWidget):
    """添加书籍按钮组件"""
    
    # 信号：添加按钮被点击
    add_book_clicked = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._setup_ui()
    
    def _setup_ui(self):
        """设置UI"""
        self.setFixedSize(
            BOOKSHELF_CONFIG['book_width'], 
            BOOKSHELF_CONFIG['book_height']
        )
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(10)
        
        # 添加按钮封面
        self.cover_label = QLabel()
        self.cover_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.cover_label.setFixedSize(
            BOOKSHELF_CONFIG['cover_width'], 
            BOOKSHELF_CONFIG['cover_height']
        )
        self.cover_label.setFrameShape(QFrame.Shape.Box)
        self.cover_label.setStyleSheet(f"""
            {BOOK_COVER_STYLE}
            border: 2px dashed #666;
            background-color: #3c3c3c;
        """)
        
        # 标题
        self.title_label = QLabel("添加书籍")
        self.title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.title_label.setStyleSheet("""
            font-weight: bold;
            font-size: 14px;
            color: #cccccc;
        """)
        
        # 提示文字
        self.hint_label = QLabel("点击添加")
        self.hint_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.hint_label.setStyleSheet("""
            font-size: 12px;
            color: #888888;
        """)
        
        # 创建添加图标
        self._create_add_icon()
        
        layout.addWidget(self.cover_label)
        layout.addWidget(self.title_label)
        layout.addWidget(self.hint_label)
    
    def _create_add_icon(self):
        """创建添加图标"""
        pixmap = QPixmap(
            BOOKSHELF_CONFIG['cover_width'], 
            BOOKSHELF_CONFIG['cover_height']
        )
        pixmap.fill(QColor(60, 60, 60))
        
        painter = QPainter(pixmap)
        painter.setPen(QColor(200, 200, 200))
        painter.setFont(QFont("Arial", 24, QFont.Weight.Bold))
        
        # 绘制加号
        rect = pixmap.rect()
        painter.drawText(rect, Qt.AlignmentFlag.AlignCenter, "+")
        
        painter.end()
        
        self.cover_label.setPixmap(pixmap)
    
    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.add_book_clicked.emit()
        super().mousePressEvent(event)
    
    def enterEvent(self, event):
        """鼠标进入事件"""
        self.cover_label.setStyleSheet(f"""
            {BOOK_COVER_STYLE}
            border: 2px dashed #0078d4;
            background-color: #4a4a4a;
        """)
        super().enterEvent(event)
    
    def leaveEvent(self, event):
        """鼠标离开事件"""
        self.cover_label.setStyleSheet(f"""
            {BOOK_COVER_STYLE}
            border: 2px dashed #666;
            background-color: #3c3c3c;
        """)
        super().leaveEvent(event)
