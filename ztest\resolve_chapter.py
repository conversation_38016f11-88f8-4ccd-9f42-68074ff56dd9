import os.path
import re
import chardet
from typing import Dict, Optional


class Chapter:
    def __init__(self, title: str, index: int = 0, position: int = 0, size: int = 0):
        self.title: str = title
        self.index: int = index
        self.position: int = position
        self.size: int = size

    def __str__(self):
        return f"Chapter(title={self.title}, index={self.index}, position={self.position}, size={self.size})"


def get_file_encoding(file_path: str, default_size: int = 4096) -> Optional[str]:
    encodings = [
        'utf-8',  # UTF-8 (最常用)
        'gbk',  # 中文 GBK
        'gb2312',  # 中文 GB2312
        'gb18030',  # 中文 GB18030
        'big5',  # 繁体中文 Big5
        'utf-16',  # UTF-16
        'utf-16le',  # UTF-16 Little Endian
        'utf-16be',  # UTF-16 Big Endian
        'ascii',  # ASCII
        'latin1',  # Latin-1 (ISO-8859-1)
        'cp1252',  # Windows-1252
        'iso-8859-1',  # ISO-8859-1
    ]

    for encoding in encodings:
        try:
            with open(file_path, 'r', encoding=encoding) as file:
                content = file.read(default_size)
                print(f"成功使用 {encoding} 编码读取文件: {file_path}")
                return encoding
        except (UnicodeDecodeError, UnicodeError):
            continue
        except Exception as e:
            print(f"使用 {encoding} 编码读取文件时出错: {e}")
            continue

    with open(file_path, 'rb') as file:
        content = file.read(default_size)
        return chardet.detect(content)['encoding']


def resolve_chapter(file_path: str, encoding: str) -> Dict[int, Chapter]:
    chapter_dict: Dict[int, Chapter] = {}
    pattern = re.compile(r'^\s*第[一二两三四五六七八九十零百千万\d]+[卷章节部篇]：[^\n\d]*\d+[^\n]*$')

    chapter_index = 1

    with open(file_path, 'rb') as file:
        for line in file:
            try:
                line_str = line.decode(encoding).strip()
            except UnicodeDecodeError:
                continue

            match_result = pattern.search(line_str)
            if match_result is not None:
                current_position = file.tell()
                if chapter_dict:
                    last_chapter = chapter_dict[chapter_index - 1]
                    last_chapter.size = current_position - last_chapter.position

                title = match_result.group().strip()
                chapter_dict[chapter_index] = Chapter(title, chapter_index, current_position)
                chapter_index += 1

        if chapter_dict:
            file_size = os.path.getsize(file_path)
            last_chapter = chapter_dict[chapter_index - 1]
            last_chapter.size = file_size - last_chapter.position
    return chapter_dict


if __name__ == '__main__':
    path = "D:/Software/Telegram Desktop/Download/（柴鸡蛋）你丫上瘾了？ 正文+番外28篇.txt"
    encode = get_file_encoding(path)
    result = resolve_chapter(path, encode)

    print(f"章节数 >> {len(result)}")
    # chapter = int(input("Select chapter >> "))
    # select_chapter = result.get(chapter)
    # if select_chapter is not None:
    #     print(f">> {select_chapter.title}")
    #     with open(path, 'r', encoding=encode) as file:
    #         file.seek(select_chapter.position)
    #         content = file.read(select_chapter.size)
    #         print(content)

    for key, value in result.items():
        print(f"{key} >> {value}")




