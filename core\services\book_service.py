"""书籍服务类"""

from typing import List, Optional, Dict, Any
from datetime import datetime
import os

from ..models.ebook import EBook
from ..database.database_manager import DatabaseManager


class BookService:
    """书籍业务逻辑服务"""

    def __init__(self, db_path: str = "books.db"):
        """
        初始化书籍服务

        Args:
            db_path: 数据库文件路径
        """
        self.db_manager = DatabaseManager(db_path)
        self._init_sample_data()

    def _init_sample_data(self):
        """初始化示例数据（仅在数据库为空时）"""
        if not self.db_manager.get_all_books():
            sample_books = [
                {
                    'title': 'Python编程',
                    'author': '<PERSON>',
                    'add_date': '2024-01-01',
                    'file_format': 'PDF',
                    'page_count': 500,
                    'file_size': 5242880  # 5MB
                },
                {
                    'title': 'Qt开发指南',
                    'author': 'Qt官方',
                    'add_date': '2024-01-02',
                    'file_format': 'PDF',
                    'page_count': 800,
                    'file_size': 8388608  # 8MB
                },
                {
                    'title': '深入理解计算机系统',
                    'author': 'Randal E. <PERSON>',
                    'add_date': '2024-01-03',
                    'file_format': 'PDF',
                    'page_count': 1200,
                    'file_size': 12582912  # 12MB
                },
                {
                    'title': '算法导论',
                    'author': 'Thomas H. Cormen',
                    'add_date': '2024-01-04',
                    'file_format': 'PDF',
                    'page_count': 1000,
                    'file_size': 10485760  # 10MB
                },
                {
                    'title': '设计模式',
                    'author': 'Erich Gamma',
                    'add_date': '2024-01-05',
                    'file_format': 'PDF',
                    'page_count': 400,
                    'file_size': 4194304  # 4MB
                },
                {
                    'title': '代码整洁之道',
                    'author': 'Robert C. Martin',
                    'add_date': '2024-01-06',
                    'file_format': 'PDF',
                    'page_count': 350,
                    'file_size': 3670016  # 3.5MB
                },
                {
                    'title': '重构：改善既有代码的设计',
                    'author': 'Martin Fowler',
                    'add_date': '2024-01-07',
                    'file_format': 'PDF',
                    'page_count': 450,
                    'file_size': 4718592  # 4.5MB
                },
                {
                    'title': 'Effective Python',
                    'author': 'Brett Slatkin',
                    'add_date': '2024-01-08',
                    'file_format': 'PDF',
                    'page_count': 300,
                    'file_size': 3145728  # 3MB
                }
            ]

            for book_data in sample_books:
                self.db_manager.insert_book(book_data)

    def get_all_books(self) -> List[EBook]:
        """获取所有书籍"""
        book_dicts = self.db_manager.get_all_books()
        return [EBook.from_dict(book_dict) for book_dict in book_dicts]

    def search_books(self, query: str) -> List[EBook]:
        """搜索书籍"""
        book_dicts = self.db_manager.search_books(query)
        return [EBook.from_dict(book_dict) for book_dict in book_dicts]

    def add_book(self, book: EBook) -> bool:
        """添加书籍"""
        try:
            # 排除ID字段，让数据库自动生成
            book_data = book.to_dict()
            if 'id' in book_data:
                del book_data['id']

            book_id = self.db_manager.insert_book(book_data)
            book.id = book_id
            return True
        except Exception as e:
            print(f"添加书籍失败: {e}")
            return False

    def update_book(self, book: EBook) -> bool:
        """更新书籍信息"""
        if not book.id:
            return False

        try:
            book_data = book.to_dict()
            # 移除ID字段，不更新ID
            if 'id' in book_data:
                del book_data['id']

            return self.db_manager.update_book(book.id, book_data)
        except Exception as e:
            print(f"更新书籍失败: {e}")
            return False

    def remove_book(self, book: EBook) -> bool:
        """移除书籍"""
        if not book.id:
            return False

        try:
            return self.db_manager.delete_book(book.id)
        except Exception as e:
            print(f"删除书籍失败: {e}")
            return False

    def get_book_by_id(self, book_id: int) -> Optional[EBook]:
        """根据ID获取书籍"""
        book_dict = self.db_manager.get_book_by_id(book_id)
        return EBook.from_dict(book_dict) if book_dict else None

    def get_book_by_title(self, title: str) -> Optional[EBook]:
        """根据标题获取书籍"""
        books = self.search_books(title)
        for book in books:
            if book.title == title:
                return book
        return None



    def update_reading_progress(self, book: EBook, progress: float) -> bool:
        """更新阅读进度"""
        if not book.id:
            return False

        try:
            book.read_progress = max(0.0, min(1.0, progress))  # 确保进度在0-1之间
            book.last_read_date = datetime.now().isoformat()
            return self.db_manager.update_reading_progress(book.id, book.read_progress)
        except Exception as e:
            print(f"更新阅读进度失败: {e}")
            return False



    def get_recently_added_books(self, limit: int = 10) -> List[EBook]:
        """获取最近添加的书籍"""
        all_books = self.get_all_books()
        # 按创建时间排序，返回最新的几本
        sorted_books = sorted(all_books, key=lambda x: x.created_at or '', reverse=True)
        return sorted_books[:limit]

    def get_recently_read_books(self, limit: int = 10) -> List[EBook]:
        """获取最近阅读的书籍"""
        all_books = self.get_all_books()
        # 过滤出有阅读记录的书籍，按最后阅读时间排序
        read_books = [book for book in all_books if book.last_read_date]
        sorted_books = sorted(read_books, key=lambda x: x.last_read_date or '', reverse=True)
        return sorted_books[:limit]

    def get_reading_statistics(self) -> Dict[str, Any]:
        """获取阅读统计信息"""
        stats = self.db_manager.get_database_stats()

        # 添加更多统计信息
        all_books = self.get_all_books()

        # 计算平均阅读进度
        total_progress = sum(book.read_progress for book in all_books)
        avg_progress = total_progress / len(all_books) if all_books else 0

        # 按格式分组统计
        format_stats = {}
        for book in all_books:
            format_name = book.file_format or '未知'
            format_stats[format_name] = format_stats.get(format_name, 0) + 1

        # 按作者分组统计
        author_stats = {}
        for book in all_books:
            author_stats[book.author] = author_stats.get(book.author, 0) + 1

        # 计算总文件大小
        total_size = sum(book.file_size or 0 for book in all_books)

        stats.update({
            'average_progress': avg_progress,
            'format_distribution': format_stats,
            'top_authors': dict(sorted(author_stats.items(), key=lambda x: x[1], reverse=True)[:5]),
            'total_file_size': total_size
        })

        return stats

    def import_book_from_file(self, file_path: str, title: str = None, author: str = None) -> Optional[EBook]:
        """从文件导入书籍"""
        if not os.path.exists(file_path):
            return None

        try:
            # 获取文件信息
            file_size = os.path.getsize(file_path)
            file_format = os.path.splitext(file_path)[1].upper().lstrip('.')

            # 如果没有提供标题和作者，从文件名推断
            if not title:
                title = os.path.splitext(os.path.basename(file_path))[0]
            if not author:
                author = "未知作者"

            # 创建书籍对象
            book = EBook(
                title=title,
                author=author,
                file_path=file_path,
                file_size=file_size,
                file_format=file_format
            )

            # 添加到数据库
            if self.add_book(book):
                return book

        except Exception as e:
            print(f"导入书籍失败: {e}")

        return None

    def export_books_data(self) -> List[Dict[str, Any]]:
        """导出书籍数据"""
        books = self.get_all_books()
        return [book.to_dict() for book in books]

    def backup_database(self, backup_path: str) -> bool:
        """备份数据库"""
        try:
            import shutil
            shutil.copy2(self.db_manager.db_path, backup_path)
            return True
        except Exception as e:
            print(f"备份数据库失败: {e}")
            return False
