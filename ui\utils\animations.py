"""UI动画工具"""

from PySide6.QtCore import QPropertyAnimation, QEasingCurve
from PySide6.QtWidgets import QWidget
from config.settings import NAVBAR_CONFIG


class AnimationUtils:
    """动画工具类"""
    
    @staticmethod
    def create_width_animation(widget: QWidget, start_width: int, end_width: int, 
                              duration: int = None) -> QPropertyAnimation:
        """创建宽度动画"""
        if duration is None:
            duration = NAVBAR_CONFIG['animation_duration']
            
        animation = QPropertyAnimation(widget, b"minimumWidth")
        animation.setDuration(duration)
        animation.setStartValue(start_width)
        animation.setEndValue(end_width)
        animation.setEasingCurve(QEasingCurve.Type.InOutQuart)
        return animation
    
    @staticmethod
    def toggle_navbar_width(navbar_widget: QWidget) -> QPropertyAnimation:
        """切换导航栏宽度"""
        current_width = navbar_widget.width()
        collapsed_width = NAVBAR_CONFIG['collapsed_width']
        expanded_width = NAVBAR_CONFIG['expanded_width']
        
        target_width = expanded_width if current_width == collapsed_width else collapsed_width
        
        return AnimationUtils.create_width_animation(
            navbar_widget, current_width, target_width
        )
