from PySide6.QtWidgets import QPushButton
from PySide6.QtCore import QSize, Qt
from PySide6.QtGui import QIcon
from config.styles import NAV_ITEM_STYLE
from config.settings import NAVBAR_CONFIG


class NavItem(QPushButton):
    """导航栏项目组件"""
    def __init__(self, text: str = "", icon_path: str = "", parent=None):
        super().__init__(text, parent)
        self.icon_path = icon_path
        self._setup_ui()

    def _setup_ui(self):
        """设置UI"""
        self.setMinimumSize(QSize(NAVBAR_CONFIG['collapsed_width'], 50))
        self.setCheckable(True)
        self.setStyleSheet(NAV_ITEM_STYLE)

        # 设置图标
        if self.icon_path:
            self.setIcon(QIcon(self.icon_path))
            self.setIconSize(QSize(24, 24))

        # 设置按钮内容居中对齐
        # 如果只有图标没有文本，图标会自动居中
        if not self.text() or self.text().strip() == "":
            # 只有图标时，确保图标居中
            pass  # QPushButton默认会将图标居中

    def set_icon_text(self, icon_text: str):
        """设置图标文本"""
        self.setText(icon_text)

    def set_icon(self, icon_path: str):
        """设置图标"""
        self.icon_path = icon_path
        if icon_path:
            self.setIcon(QIcon(icon_path))
            self.setIconSize(QSize(24, 24))

    def set_active(self, active: bool):
        """设置激活状态"""
        self.setChecked(active)
