"""样式配置"""

# 深色主题颜色配置
COLORS = {
    'primary_bg': '#2b2b2b',
    'secondary_bg': '#3c3c3c',
    'accent_bg': '#1e3a5f',
    'accent_color': '#0078d4',
    'accent_hover': '#106ebe',
    'text_primary': '#ffffff',
    'text_secondary': '#cccccc',
    'border_color': '#555555',
    'hover_bg': '#4a4a4a',
    'button_primary': '#4cc2ff',
    'button_hover': '#48b2e9',
    'success': '#28a745',
    'warning': '#ffc107',
    'error': '#dc3545'
}

# 导航项样式
NAV_ITEM_STYLE = f"""
    QPushButton {{
        background-color: transparent;
        border: none;
        color: {COLORS['text_secondary']};
        padding: 0px;
        margin: 0px;
        font-size: 14px;
        qproperty-iconSize: 24px 24px;
    }}
    QPushButton:hover {{
        background-color: {COLORS['hover_bg']};
        color: {COLORS['text_primary']};
    }}
    QPushButton:checked {{
        border-left: 3px solid {COLORS['accent_color']};
        color: {COLORS['text_primary']};
        font-weight: bold;
    }}
"""

# 搜索框样式
SEARCH_INPUT_STYLE = f"""
    QLineEdit {{
        padding: 8px;
        border: 1px solid {COLORS['border_color']};
        border-radius: 4px;
        font-size: 14px;
        background-color: {COLORS['primary_bg']};
        color: {COLORS['text_primary']};
    }}
"""

# 搜索按钮样式
SEARCH_BUTTON_STYLE = f"""
    QPushButton {{
        padding: 8px;
        background-color: {COLORS['button_primary']};
        color: black;
        border: none;
        border-radius: 4px;
        font-size: 12px;
    }}
    QPushButton:hover {{
        background-color: {COLORS['button_hover']};
    }}
"""

# 书籍封面样式
BOOK_COVER_STYLE = f"""
    background-color: {COLORS['secondary_bg']};
    border: 1px solid {COLORS['border_color']};
"""

# 书籍标题样式
BOOK_TITLE_STYLE = f"""
    font-weight: bold;
    font-size: 14px;
    color: {COLORS['text_primary']};
"""

# 书籍作者样式
BOOK_AUTHOR_STYLE = f"""
    font-size: 12px;
    color: {COLORS['text_secondary']};
"""
