"""设置页面"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                               QFrame, QSpacerItem, QSizePolicy, QGroupBox)
from PySide6.QtCore import Qt
from config import styles


class SettingsPage(QWidget):
    """设置页面"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self._setup_ui()
    
    def _setup_ui(self):
        """设置UI"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(30, 30, 30, 30)
        main_layout.setSpacing(30)

        # 页面标题
        title_label = QLabel("设置")
        title_label.setStyleSheet(f"""
            QLabel {{
                color: {styles.COLORS['text_primary']};
                font-size: 24px;
                font-weight: bold;
                padding: 10px 0px;
            }}
        """)
        main_layout.addWidget(title_label)

        # 设置说明
        info_label = QLabel("这里是应用程序的设置页面。")
        info_label.setStyleSheet(f"""
            QLabel {{
                color: {styles.COLORS['text_secondary']};
                font-size: 14px;
                padding: 10px 0px;
            }}
        """)
        main_layout.addWidget(info_label)

        # 添加弹性空间
        main_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding))

