"""数据库管理器"""

import sqlite3
import os
from typing import List, Optional, Dict, Any
from datetime import datetime
from contextlib import contextmanager


class DatabaseManager:
    """SQLite数据库管理器"""
    
    def __init__(self, db_path: str = "books.db"):
        """
        初始化数据库管理器
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path
        self._init_database()
    
    def _init_database(self):
        """初始化数据库，创建表结构"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # 创建书籍表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS books (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title TEXT NOT NULL,
                    author TEXT NOT NULL,
                    file_path TEXT,
                    add_date TEXT NOT NULL,
                    page_count INTEGER,
                    file_size INTEGER,
                    file_format TEXT,
                    read_progress REAL DEFAULT 0.0,
                    last_read_date TEXT,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL
                )
            ''')

            
            conn.commit()
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接的上下文管理器"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # 使结果可以通过列名访问
        try:
            yield conn
        finally:
            conn.close()
    
    def insert_book(self, book_data: Dict[str, Any]) -> int:
        """
        插入新书籍
        
        Args:
            book_data: 书籍数据字典
            
        Returns:
            新插入书籍的ID
        """
        now = datetime.now().isoformat()
        book_data['created_at'] = now
        book_data['updated_at'] = now
        
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            columns = ', '.join(book_data.keys())
            placeholders = ', '.join(['?' for _ in book_data])
            values = list(book_data.values())
            
            cursor.execute(f'''
                INSERT INTO books ({columns})
                VALUES ({placeholders})
            ''', values)
            
            conn.commit()
            return cursor.lastrowid
    
    def update_book(self, book_id: int, book_data: Dict[str, Any]) -> bool:
        """
        更新书籍信息
        
        Args:
            book_id: 书籍ID
            book_data: 要更新的数据字典
            
        Returns:
            是否更新成功
        """
        if not book_data:
            return False
        
        book_data['updated_at'] = datetime.now().isoformat()
        
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            set_clause = ', '.join([f'{key} = ?' for key in book_data.keys()])
            values = list(book_data.values()) + [book_id]
            
            cursor.execute(f'''
                UPDATE books 
                SET {set_clause}
                WHERE id = ?
            ''', values)
            
            conn.commit()
            return cursor.rowcount > 0
    
    def delete_book(self, book_id: int) -> bool:
        """
        删除书籍
        
        Args:
            book_id: 书籍ID
            
        Returns:
            是否删除成功
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('DELETE FROM books WHERE id = ?', (book_id,))
            conn.commit()
            return cursor.rowcount > 0
    
    def get_book_by_id(self, book_id: int) -> Optional[Dict[str, Any]]:
        """
        根据ID获取书籍
        
        Args:
            book_id: 书籍ID
            
        Returns:
            书籍数据字典或None
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM books WHERE id = ?', (book_id,))
            row = cursor.fetchone()
            return dict(row) if row else None
    
    def get_all_books(self) -> List[Dict[str, Any]]:
        """
        获取所有书籍
        
        Returns:
            书籍数据字典列表
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM books ORDER BY created_at DESC')
            rows = cursor.fetchall()
            return [dict(row) for row in rows]
    
    def search_books(self, query: str) -> List[Dict[str, Any]]:
        """
        搜索书籍

        Args:
            query: 搜索关键词

        Returns:
            匹配的书籍数据字典列表
        """
        if not query.strip():
            return self.get_all_books()

        with self.get_connection() as conn:
            cursor = conn.cursor()
            search_pattern = f'%{query}%'
            cursor.execute('''
                SELECT * FROM books
                WHERE title LIKE ? OR author LIKE ?
                ORDER BY
                    CASE
                        WHEN title LIKE ? THEN 1
                        WHEN author LIKE ? THEN 2
                        ELSE 3
                    END,
                    created_at DESC
            ''', (search_pattern, search_pattern, search_pattern, search_pattern))

            rows = cursor.fetchall()
            return [dict(row) for row in rows]


    def update_reading_progress(self, book_id: int, progress: float) -> bool:
        """
        更新阅读进度

        Args:
            book_id: 书籍ID
            progress: 阅读进度 (0.0-1.0)

        Returns:
            是否更新成功
        """
        return self.update_book(book_id, {
            'read_progress': progress,
            'last_read_date': datetime.now().isoformat()
        })
    
    def get_database_stats(self) -> Dict[str, int]:
        """
        获取数据库统计信息

        Returns:
            统计信息字典
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # 书籍总数
            cursor.execute('SELECT COUNT(*) FROM books')
            total_books = cursor.fetchone()[0]

            # 已读书籍数（进度 > 0.9）
            cursor.execute('SELECT COUNT(*) FROM books WHERE read_progress > 0.9')
            read_books = cursor.fetchone()[0]

            # 正在阅读的书籍数（进度 > 0 且 <= 0.9）
            cursor.execute('SELECT COUNT(*) FROM books WHERE read_progress > 0 AND read_progress <= 0.9')
            reading_books = cursor.fetchone()[0]

            return {
                'total_books': total_books,
                'read_books': read_books,
                'reading_books': reading_books
            }
