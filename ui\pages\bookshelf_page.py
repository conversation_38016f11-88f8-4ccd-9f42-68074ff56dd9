"""书架页面"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLineEdit,
                               QPushButton, QScrollArea, QGridLayout, QFrame,
                               QFileDialog, QLabel, QMessageBox)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QIcon
import os
from datetime import datetime

from core.models.ebook import EBook
from core.services.book_service import BookService
from ui.components.book_widget import BookWidget
from ui.components.add_book_widget import AddBookWidget
from config.settings import BOOKSHELF_CONFIG
from config.styles import SEARCH_INPUT_STYLE, SEARCH_BUTTON_STYLE


class BookshelfPage(QWidget):
    """书架页面"""

    # 信号：书籍被选中
    book_selected = Signal(EBook)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.book_service = BookService()
        self.book_widgets = []
        self.books_per_row = BOOKSHELF_CONFIG['books_per_row']
        self._setup_ui()
        self._load_books()
    
    def _setup_ui(self):
        """设置UI"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(15)

        # 搜索栏
        search_bar = self._create_search_bar()

        # 控制面板
        control_panel = self._create_control_panel()

        # 书架网格区域
        scroll_area = self._create_bookshelf_area()

        main_layout.addWidget(search_bar)
        main_layout.addWidget(control_panel)
        main_layout.addWidget(scroll_area)
    
    def _create_search_bar(self) -> QWidget:
        """创建搜索栏"""
        search_bar = QWidget()
        search_layout = QHBoxLayout(search_bar)
        search_layout.setContentsMargins(0, 0, 0, 0)
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("搜索书籍...")
        self.search_input.setClearButtonEnabled(True)
        self.search_input.setStyleSheet(SEARCH_INPUT_STYLE)
        self.search_input.textChanged.connect(self._on_search_text_changed)
        
        self.search_button = QPushButton("Search")
        self.search_button.setFixedWidth(80)
        self.search_button.setStyleSheet(SEARCH_BUTTON_STYLE)
        self.search_button.clicked.connect(self._perform_search)

        search_layout.addWidget(self.search_input)
        search_layout.addWidget(self.search_button)
        
        return search_bar

    def _create_control_panel(self) -> QWidget:
        """创建控制面板"""
        control_panel = QWidget()
        control_layout = QHBoxLayout(control_panel)
        control_layout.setContentsMargins(0, 0, 0, 0)

        # 显示当前每行书籍数量（只读）
        self.books_per_row_label = QLabel(f"每行书籍数: {self.books_per_row}")
        self.books_per_row_label.setStyleSheet("color: #cccccc; font-size: 12px;")

        # 删除模式按钮
        self.delete_mode_button = QPushButton("删除模式")
        self.delete_mode_button.setCheckable(True)
        self.delete_mode_button.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
            QPushButton:checked {
                background-color: #a71e2a;
            }
        """)
        self.delete_mode_button.toggled.connect(self._on_delete_mode_toggled)

        # 添加到布局
        control_layout.addWidget(self.books_per_row_label)
        control_layout.addStretch()
        control_layout.addWidget(self.delete_mode_button)

        return control_panel

    def _create_bookshelf_area(self) -> QScrollArea:
        """创建书架区域"""
        self.bookshelf_scroll = QScrollArea()
        self.bookshelf_scroll.setWidgetResizable(True)
        self.bookshelf_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.bookshelf_scroll.setFrameShape(QFrame.Shape.NoFrame)

        self.bookshelf_container = QWidget()
        self.bookshelf_layout = QGridLayout(self.bookshelf_container)
        self.bookshelf_layout.setSpacing(25)
        self.bookshelf_layout.setContentsMargins(15, 15, 15, 15)
        self.bookshelf_layout.setAlignment(Qt.AlignmentFlag.AlignTop)

        self.bookshelf_scroll.setWidget(self.bookshelf_container)
        return self.bookshelf_scroll

    def _calculate_books_per_row(self) -> int:
        """根据页面宽度动态计算每行书籍数量"""
        # 获取滚动区域的可用宽度
        available_width = self.bookshelf_scroll.viewport().width()

        # 书籍宽度 + 间距
        book_width = BOOKSHELF_CONFIG['book_width']
        spacing = 10  # 网格布局的间距

        # 计算每行可以放置的书籍数量
        books_per_row = max(2, (available_width + spacing) // (book_width + spacing))

        # 限制最大数量，避免书籍过小
        books_per_row = min(books_per_row, 8)

        return books_per_row

    def _update_books_per_row(self):
        """更新每行书籍数量"""
        new_books_per_row = self._calculate_books_per_row()
        if new_books_per_row != self.books_per_row:
            self.books_per_row = new_books_per_row
            # 更新显示标签
            if hasattr(self, 'books_per_row_label'):
                self.books_per_row_label.setText(f"每行书籍数: {self.books_per_row}")
            # 重新显示书籍
            books = self.book_service.get_all_books()
            self._display_books(books)

    def _initial_layout_update(self):
        """初始布局更新"""
        if hasattr(self, 'bookshelf_scroll'):
            self._update_books_per_row()

    def showEvent(self, event):
        """页面显示事件"""
        super().showEvent(event)
        # 页面显示时触发布局更新
        from PySide6.QtCore import QTimer
        QTimer.singleShot(30, self._initial_layout_update)  # 更早触发
        QTimer.singleShot(100, self._initial_layout_update)  # 双重保险

    def resizeEvent(self, event):
        """窗口大小改变事件"""
        super().resizeEvent(event)
        # 延迟更新，避免频繁重绘
        if hasattr(self, 'bookshelf_scroll'):
            self._update_books_per_row()

    def _load_books(self):
        """加载书籍"""
        books = self.book_service.get_all_books()
        self._display_books(books)

        # 延迟执行初始布局计算，确保滚动区域已经正确渲染
        from PySide6.QtCore import QTimer
        QTimer.singleShot(50, self._initial_layout_update)  # 更早触发
        QTimer.singleShot(150, self._initial_layout_update)  # 双重保险
    
    def _display_books(self, books):
        """显示书籍"""
        # 清除现有的书籍组件
        self._clear_books()

        # 首先添加"添加书籍"按钮
        add_widget = AddBookWidget()
        add_widget.add_book_clicked.connect(self._on_add_book_clicked)
        self.bookshelf_layout.addWidget(add_widget, 0, 0)
        self.book_widgets.append(add_widget)

        # 添加书籍组件
        delete_mode = hasattr(self, 'delete_mode_button') and self.delete_mode_button.isChecked()

        for i, book in enumerate(books):
            book_widget = BookWidget(book, show_delete_button=delete_mode)
            book_widget.book_clicked.connect(self._on_book_clicked)

            # 连接删除信号
            if delete_mode:
                book_widget.delete_clicked.connect(self._on_book_delete_clicked)

            # 计算网格位置（考虑添加按钮占用第一个位置）
            total_index = i + 1  # +1 因为第一个位置是添加按钮
            row = total_index // self.books_per_row
            col = total_index % self.books_per_row

            self.bookshelf_layout.addWidget(book_widget, row, col)
            self.book_widgets.append(book_widget)
    
    def _clear_books(self):
        """清除书籍显示"""
        for widget in self.book_widgets:
            widget.deleteLater()
        self.book_widgets.clear()
    
    def _on_search_text_changed(self, text: str):
        """搜索文本改变事件"""
        # 可以在这里添加实时搜索功能
        pass
    
    def _perform_search(self):
        """执行搜索"""
        query = self.search_input.text().strip()
        books = self.book_service.search_books(query)
        self._display_books(books)
    
    def _on_book_clicked(self, book: EBook):
        """书籍点击事件"""
        print(f"书籍被点击: {book.title}")
        self.book_selected.emit(book)

    def _on_book_delete_clicked(self, book: EBook):
        """书籍删除按钮点击事件"""
        reply = QMessageBox.question(
            self,
            "确认删除",
            f"确定要删除书籍 '{book.title}' 吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            if self.book_service.remove_book(book):
                QMessageBox.information(self, "成功", f"书籍 '{book.title}' 已删除！")
                self.refresh_books()
            else:
                QMessageBox.warning(self, "错误", "删除书籍失败！")

    def _on_add_book_clicked(self):
        """添加书籍按钮点击事件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择书籍文件",
            "",
            "文本文件 (*.txt);;所有文件 (*)"
        )

        if file_path:
            self._add_book_from_file(file_path)

    def _add_book_from_file(self, file_path: str):
        """从文件添加书籍"""
        try:
            # 获取文件信息
            file_name = os.path.basename(file_path)
            title = os.path.splitext(file_name)[0]  # 去掉扩展名作为标题
            file_size = os.path.getsize(file_path)

            # 创建书籍对象
            book = EBook(
                title=title,
                author="未知作者",  # 默认作者
                file_path=file_path,
                file_format="TXT",
                file_size=file_size,
                add_date=datetime.now().strftime("%Y-%m-%d")
            )

            # 添加到数据库
            if self.book_service.add_book(book):
                QMessageBox.information(self, "成功", f"书籍 '{title}' 添加成功！")
                self.refresh_books()
            else:
                QMessageBox.warning(self, "错误", "添加书籍失败！")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"添加书籍时发生错误：{str(e)}")

    def _on_delete_mode_toggled(self, checked: bool):
        """删除模式切换事件"""
        if checked:
            self.delete_mode_button.setText("退出删除")
        else:
            self.delete_mode_button.setText("删除模式")

        # 重新显示书籍以应用删除模式
        books = self.book_service.get_all_books()
        self._display_books(books)

    def _show_delete_menu(self, pos, book: EBook):
        """显示删除菜单"""
        reply = QMessageBox.question(
            self,
            "确认删除",
            f"确定要删除书籍 '{book.title}' 吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            if self.book_service.remove_book(book):
                QMessageBox.information(self, "成功", f"书籍 '{book.title}' 已删除！")
                self.refresh_books()
            else:
                QMessageBox.warning(self, "错误", "删除书籍失败！")

    def refresh_books(self):
        """刷新书籍显示"""
        self._load_books()

