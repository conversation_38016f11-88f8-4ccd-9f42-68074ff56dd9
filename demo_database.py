"""SQLite数据库功能演示"""

import os
import sys
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.models.ebook import EBook
from core.services.book_service import BookService


def demo_database_features():
    """演示数据库功能"""
    print("🚀 SQLite数据库功能演示")
    print("=" * 60)
    
    # 初始化书籍服务
    book_service = BookService()
    
    # 1. 显示初始数据
    print("\n📚 1. 显示所有书籍")
    print("-" * 40)
    books = book_service.get_all_books()
    for i, book in enumerate(books[:5], 1):  # 只显示前5本
        print(f"{i}. {book.title} - {book.author}")
        print(f"   格式: {book.file_format or '未知'}")
        if book.page_count:
            print(f"   页数: {book.page_count}")
        if book.file_size:
            print(f"   大小: {book.file_size_display}")
        print()
    
    print(f"总共有 {len(books)} 本书籍")
    
    # 2. 搜索功能演示
    print("\n🔍 2. 搜索功能演示")
    print("-" * 40)
    search_results = book_service.search_books("Python")
    print(f"搜索'Python'找到 {len(search_results)} 本书:")
    for book in search_results:
        print(f"- {book.title} - {book.author}")
    
    # 3. 添加新书籍
    print("\n➕ 3. 添加新书籍")
    print("-" * 40)
    new_book = EBook(
        title="SQLite实战指南",
        author="数据库专家",
        file_format="PDF",
        page_count=350,
        file_size=3670016  # 3.5MB
    )
    
    if book_service.add_book(new_book):
        print(f"✅ 成功添加新书: {new_book.title} (ID: {new_book.id})")
    else:
        print("❌ 添加书籍失败")
    
    # 4. 更新书籍信息
    print("\n✏️ 4. 更新书籍信息")
    print("-" * 40)
    if new_book.id:
        new_book.file_path = "/path/to/sqlite_guide.pdf"
        new_book.page_count = 400  # 更新页数

        if book_service.update_book(new_book):
            print("✅ 书籍信息更新成功")
            print(f"   文件路径: {new_book.file_path}")
            print(f"   页数: {new_book.page_count}")
        else:
            print("❌ 更新失败")
    
    # 5. 文件信息显示
    print("\n📁 5. 文件信息显示")
    print("-" * 40)
    if new_book.id:
        print(f"📖 书籍: {new_book.title}")
        print(f"👤 作者: {new_book.author}")
        print(f"📄 页数: {new_book.page_count}")
        print(f"💾 文件大小: {new_book.file_size_display}")
        print(f"📋 格式: {new_book.file_format}")
        if new_book.file_path:
            print(f"📂 路径: {new_book.file_path}")
    
    # 6. 阅读进度功能
    print("\n📈 6. 阅读进度功能")
    print("-" * 40)
    if new_book.id:
        # 更新阅读进度
        progress_values = [0.2, 0.5, 0.8]
        for progress in progress_values:
            book_service.update_reading_progress(new_book, progress)
            print(f"📖 阅读进度更新到: {progress:.1%}")
        
        # 显示最终状态
        updated_book = book_service.get_book_by_id(new_book.id)
        if updated_book:
            print(f"✅ 当前阅读状态: {updated_book.reading_status}")
            print(f"📅 最后阅读时间: {updated_book.last_read_date}")
    
    # 7. 统计信息
    print("\n📊 7. 统计信息")
    print("-" * 40)
    stats = book_service.get_reading_statistics()
    print(f"📚 总书籍数: {stats['total_books']}")
    print(f"✅ 已读书籍数: {stats['read_books']}")
    print(f"📖 正在阅读: {stats['reading_books']}")
    print(f"📈 平均阅读进度: {stats['average_progress']:.1%}")

    if 'total_file_size' in stats:
        size_mb = stats['total_file_size'] / (1024 * 1024)
        print(f"💾 总文件大小: {size_mb:.1f} MB")

    print("\n📁 文件格式分布:")
    for format_name, count in stats['format_distribution'].items():
        print(f"   {format_name}: {count} 本")

    print("\n👨‍💼 热门作者:")
    for author, count in list(stats['top_authors'].items())[:3]:
        print(f"   {author}: {count} 本")
    
    # 8. 最近添加的书籍
    print("\n🆕 8. 最近添加的书籍")
    print("-" * 40)
    recent_books = book_service.get_recently_added_books(3)
    for i, book in enumerate(recent_books, 1):
        print(f"{i}. {book.title} - {book.author}")
        print(f"   添加时间: {book.add_date}")
    
    # 9. 高级搜索演示
    print("\n🔍 9. 高级搜索演示")
    print("-" * 40)

    # 按作者搜索
    author_results = book_service.search_books("Martin")
    print(f"搜索作者'Martin': {len(author_results)} 本书")
    for book in author_results:
        print(f"   - {book.title}")

    # 按标题搜索
    title_results = book_service.search_books("Python")
    print(f"搜索标题'Python': {len(title_results)} 本书")
    for book in title_results:
        print(f"   - {book.title}")
    
    # 10. 数据导出演示
    print("\n💾 10. 数据导出演示")
    print("-" * 40)
    
    # 导出所有书籍数据
    export_data = book_service.export_books_data()
    print(f"✅ 导出了 {len(export_data)} 本书籍的数据")
    
    # 显示第一本书的详细信息
    if export_data:
        first_book = export_data[0]
        print("📖 第一本书的详细信息:")
        for key, value in first_book.items():
            if value is not None and key in ['title', 'author', 'file_format', 'page_count', 'file_size', 'read_progress']:
                if key == 'file_size' and isinstance(value, int):
                    size_mb = value / (1024 * 1024)
                    print(f"   {key}: {size_mb:.1f} MB")
                else:
                    print(f"   {key}: {value}")
    
    # 11. 清理演示数据
    print("\n🧹 11. 清理演示数据")
    print("-" * 40)
    if new_book.id:
        if book_service.remove_book(new_book):
            print(f"✅ 已删除演示书籍: {new_book.title}")
        else:
            print("❌ 删除失败")
    
    # 最终统计
    final_books = book_service.get_all_books()
    print(f"📚 最终书籍数量: {len(final_books)}")
    
    print("\n🎉 SQLite数据库功能演示完成！")
    print("=" * 60)
    
    # 显示数据库文件信息
    db_path = book_service.db_manager.db_path
    if os.path.exists(db_path):
        file_size = os.path.getsize(db_path)
        print(f"📁 数据库文件: {db_path}")
        print(f"💾 文件大小: {file_size} 字节")
    
    print("\n💡 提示:")
    print("- 数据库文件会自动保存所有更改")
    print("- 重启应用程序后数据仍然存在")
    print("- 可以使用 database_tools.py 进行高级管理")
    print("- 可以使用 test_database.py 运行完整测试")


if __name__ == "__main__":
    demo_database_features()
