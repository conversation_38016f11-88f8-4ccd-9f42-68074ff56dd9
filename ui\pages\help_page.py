"""帮助页面"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                               QScrollArea, QFrame, QSpacerItem, QSizePolicy)
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont, QPixmap
from config import styles
from config.settings import WINDOW_CONFIG


class HelpPage(QWidget):
    """帮助页面"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._setup_ui()
    
    def _setup_ui(self):
        """设置UI"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet(f"""
            QScrollArea {{
                border: none;
                background-color: {styles.COLORS['primary_bg']};
            }}
            QScrollBar:vertical {{
                background-color: {styles.COLORS['secondary_bg']};
                width: 12px;
                border-radius: 6px;
            }}
            QScrollBar::handle:vertical {{
                background-color: {styles.COLORS['accent_color']};
                border-radius: 6px;
                min-height: 20px;
            }}
        """)
        
        # 内容容器
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setSpacing(30)
        
        # 应用信息部分
        self._create_app_info_section(content_layout)
        
        # 功能介绍部分
        self._create_features_section(content_layout)
        
        # 使用说明部分
        self._create_usage_section(content_layout)
        
        # 作者信息部分
        self._create_author_section(content_layout)
        
        # 版本信息部分
        self._create_version_section(content_layout)
        
        # 添加弹性空间
        content_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding))
        
        scroll_area.setWidget(content_widget)
        main_layout.addWidget(scroll_area)
    
    def _create_section_title(self, title: str) -> QLabel:
        """创建章节标题"""
        label = QLabel(title)
        label.setStyleSheet(f"""
            QLabel {{
                color: {styles.COLORS['text_primary']};
                font-size: 20px;
                font-weight: bold;
                padding: 10px 0px;
                border-bottom: 2px solid {styles.COLORS['accent_color']};
            }}
        """)
        return label
    
    def _create_content_label(self, text: str) -> QLabel:
        """创建内容标签"""
        label = QLabel(text)
        label.setWordWrap(True)
        label.setStyleSheet(f"""
            QLabel {{
                color: {styles.COLORS['text_secondary']};
                font-size: 14px;
                line-height: 1.6;
                padding: 10px 0px;
            }}
        """)
        return label
    
    def _create_app_info_section(self, layout: QVBoxLayout):
        """创建应用信息部分"""
        title = self._create_section_title("📖 关于 Reader")
        layout.addWidget(title)
        
        info_text = """
        Reader 是一个现代化的阅读器应用程序，基于 PySide6 框架开发。
        
        本应用采用清晰的架构设计和模块化结构，提供直观的用户界面和丰富的功能，
        让您能够轻松管理和阅读您的电子书籍收藏。
        """
        
        info_label = self._create_content_label(info_text.strip())
        layout.addWidget(info_label)
    
    def _create_features_section(self, layout: QVBoxLayout):
        """创建功能介绍部分"""
        title = self._create_section_title("✨ 主要功能")
        layout.addWidget(title)
        
        features_text = """
        🏠 主页：应用程序欢迎页面和概览
        
        📚 书架管理：浏览、搜索和管理您的书籍收藏
        
        ⚙️ 设置：个性化配置，包括主题切换等选项
        
        ❓ 帮助：查看应用程序信息和使用说明
        
        🎨 现代化界面：响应式设计，支持深色和浅色主题
        
        🔍 智能搜索：快速查找您需要的书籍
        """
        
        features_label = self._create_content_label(features_text.strip())
        layout.addWidget(features_label)
    
    def _create_usage_section(self, layout: QVBoxLayout):
        """创建使用说明部分"""
        title = self._create_section_title("📋 使用说明")
        layout.addWidget(title)
        
        usage_text = """
        1. 导航栏：点击左侧导航栏的图标可以在不同页面间切换
        
        2. 书架：在书架页面可以浏览所有书籍，使用搜索框快速查找
        
        3. 设置：在设置页面可以切换应用主题（深色/浅色模式）
        
        4. 响应式设计：界面会根据窗口大小自动调整布局
        
        5. 键盘快捷键：支持常用的键盘操作（开发中）
        """
        
        usage_label = self._create_content_label(usage_text.strip())
        layout.addWidget(usage_label)
    
    def _create_author_section(self, layout: QVBoxLayout):
        """创建作者信息部分"""
        title = self._create_section_title("👨‍💻 开发者")
        layout.addWidget(title)
        
        author_text = """
        开发者：Xxyh-Bytes
        
        这是一个专注于用户体验和代码质量的现代化阅读器应用程序。
        如果您有任何建议或发现问题，欢迎反馈。
        
        感谢您使用 Reader！
        """
        
        author_label = self._create_content_label(author_text.strip())
        layout.addWidget(author_label)
    
    def _create_version_section(self, layout: QVBoxLayout):
        """创建版本信息部分"""
        title = self._create_section_title("📦 版本信息")
        layout.addWidget(title)
        
        version_text = """
        版本：1.0.0
        
        构建日期：2024年
        
        技术栈：
        • Python 3.8+
        • PySide6 (Qt6)
        • 现代化架构设计
        
        更新日志：
        • v1.0.0：初始版本，包含基础功能和现代化UI设计
        """
        
        version_label = self._create_content_label(version_text.strip())
        layout.addWidget(version_label)


