"""书籍数据模型"""

from dataclasses import dataclass, field
from typing import Optional, List, Dict, Any
from datetime import datetime


@dataclass
class EBook:
    """书籍数据模型"""
    title: str
    author: str
    id: Optional[int] = None
    file_path: Optional[str] = None
    add_date: Optional[str] = None
    page_count: Optional[int] = None
    file_size: Optional[int] = None
    file_format: Optional[str] = None
    read_progress: float = 0.0
    last_read_date: Optional[str] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None

    def __post_init__(self):
        """初始化后处理"""
        if self.add_date is None:
            self.add_date = datetime.now().strftime("%Y-%m-%d")
        if self.created_at is None:
            self.created_at = datetime.now().isoformat()
        if self.updated_at is None:
            self.updated_at = datetime.now().isoformat()

    def __str__(self) -> str:
        return f"{self.title} - {self.author}"

    @property
    def display_title(self) -> str:
        """获取显示用的标题（处理过长标题）"""
        if len(self.title) > 20:
            return self.title[:17] + "..."
        return self.title

    @property
    def display_author(self) -> str:
        """获取显示用的作者名（处理过长作者名）"""
        if len(self.author) > 15:
            return self.author[:12] + "..."
        return self.author


    @property
    def is_read(self) -> bool:
        """检查是否已读完（进度 > 90%）"""
        return self.read_progress > 0.9

    @property
    def reading_status(self) -> str:
        """获取阅读状态"""
        if self.read_progress == 0:
            return "未开始"
        elif self.read_progress >= 1.0:
            return "已完成"
        elif self.read_progress > 0.9:
            return "即将完成"
        else:
            return f"进行中 ({self.read_progress:.1%})"

    @property
    def file_size_display(self) -> str:
        """获取文件大小的显示格式"""
        if not self.file_size:
            return "未知"

        size = self.file_size
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024:
                return f"{size:.1f} {unit}"
            size /= 1024
        return f"{size:.1f} TB"

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式（用于数据库存储）"""
        return {
            'id': self.id,
            'title': self.title,
            'author': self.author,
            'file_path': self.file_path,
            'add_date': self.add_date,
            'page_count': self.page_count,
            'file_size': self.file_size,
            'file_format': self.file_format,
            'read_progress': self.read_progress,
            'last_read_date': self.last_read_date,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'EBook':
        """从字典创建EBook实例（用于数据库读取）"""
        return cls(
            title=data['title'],
            author=data['author'],
            id=data.get('id'),
            file_path=data.get('file_path'),
            add_date=data.get('add_date'),
            page_count=data.get('page_count'),
            file_size=data.get('file_size'),
            file_format=data.get('file_format'),
            read_progress=data.get('read_progress', 0.0),
            last_read_date=data.get('last_read_date'),
            created_at=data.get('created_at'),
            updated_at=data.get('updated_at')
        )
