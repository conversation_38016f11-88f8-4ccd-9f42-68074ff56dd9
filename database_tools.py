"""数据库管理工具"""

import os
import sys
import json
from datetime import datetime
from typing import List, Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.models.ebook import EBook
from core.services.book_service import BookService
from core.database.database_manager import DatabaseManager


class DatabaseTools:
    """数据库管理工具类"""
    
    def __init__(self, db_path: str = "books.db"):
        self.book_service = BookService(db_path)
        self.db_manager = self.book_service.db_manager
    
    def show_statistics(self):
        """显示数据库统计信息"""
        print("📊 数据库统计信息")
        print("=" * 50)
        
        stats = self.book_service.get_reading_statistics()
        
        print(f"📚 总书籍数: {stats['total_books']}")
        print(f"✅ 已读书籍数: {stats['read_books']}")
        print(f"📖 正在阅读: {stats['reading_books']}")
        print(f"📈 平均阅读进度: {stats['average_progress']:.1%}")
        if 'total_file_size' in stats:
            size_mb = stats['total_file_size'] / (1024 * 1024)
            print(f"💾 总文件大小: {size_mb:.1f} MB")
        
        print("\n📁 文件格式分布:")
        for format_name, count in stats['format_distribution'].items():
            print(f"   {format_name}: {count} 本")
        
        print("\n👨‍💼 热门作者 (前5名):")
        for author, count in stats['top_authors'].items():
            print(f"   {author}: {count} 本")
    
    def list_all_books(self):
        """列出所有书籍"""
        print("📚 所有书籍列表")
        print("=" * 80)
        
        books = self.book_service.get_all_books()
        
        if not books:
            print("📭 暂无书籍")
            return
        
        for i, book in enumerate(books, 1):
            progress_bar = self._get_progress_bar(book.read_progress)

            print(f"{i:3d}. 📖 {book.title}")
            print(f"     作者: {book.author}")
            print(f"     进度: {progress_bar} {book.read_progress:.1%}")
            print(f"     添加: {book.add_date}")
            if book.file_format:
                print(f"     格式: {book.file_format}")
            if book.file_size:
                print(f"     大小: {book.file_size_display}")
            print()
    
    def search_books(self, query: str):
        """搜索书籍"""
        print(f"🔍 搜索结果: '{query}'")
        print("=" * 50)
        
        books = self.book_service.search_books(query)
        
        if not books:
            print("🚫 未找到匹配的书籍")
            return
        
        for i, book in enumerate(books, 1):
            print(f"{i}. {book.title} - {book.author}")
            if book.description:
                print(f"   描述: {book.description[:100]}...")
            print()
    
    def add_sample_book(self):
        """添加示例书籍"""
        sample_book = EBook(
            title=f"示例书籍 {datetime.now().strftime('%H:%M:%S')}",
            author="示例作者",
            file_format="PDF",
            page_count=200,
            file_size=2097152  # 2MB
        )
        
        if self.book_service.add_book(sample_book):
            print(f"✅ 成功添加示例书籍: {sample_book.title} (ID: {sample_book.id})")
        else:
            print("❌ 添加示例书籍失败")
    
    def export_to_json(self, output_file: str = "books_export.json"):
        """导出书籍数据到JSON文件"""
        try:
            books_data = self.book_service.export_books_data()
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(books_data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 成功导出 {len(books_data)} 本书籍到 {output_file}")
        except Exception as e:
            print(f"❌ 导出失败: {e}")
    
    def backup_database(self, backup_file: str = None):
        """备份数据库"""
        if not backup_file:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = f"books_backup_{timestamp}.db"
        
        if self.book_service.backup_database(backup_file):
            print(f"✅ 数据库备份成功: {backup_file}")
        else:
            print("❌ 数据库备份失败")
    
    def show_recent_books(self, limit: int = 10):
        """显示最近添加的书籍"""
        print(f"🆕 最近添加的 {limit} 本书籍")
        print("=" * 50)
        
        books = self.book_service.get_recently_added_books(limit)
        
        if not books:
            print("📭 暂无书籍")
            return
        
        for i, book in enumerate(books, 1):
            print(f"{i}. {book.title} - {book.author}")
            print(f"   添加时间: {book.add_date}")
            print()
    

    
    def _get_progress_bar(self, progress: float, width: int = 20) -> str:
        """生成进度条"""
        filled = int(progress * width)
        bar = "█" * filled + "░" * (width - filled)
        return f"[{bar}]"


def show_menu():
    """显示菜单"""
    print("\n" + "=" * 60)
    print("📚 Reader 数据库管理工具")
    print("=" * 60)
    print("1. 📊 显示统计信息")
    print("2. 📚 列出所有书籍")
    print("3. 🔍 搜索书籍")
    print("4. ➕ 添加示例书籍")
    print("5. 🆕 显示最近添加的书籍")
    print("6. 💾 导出数据到JSON")
    print("7. 🔄 备份数据库")
    print("8. 🚪 退出")
    print("=" * 60)


def main():
    """主函数"""
    print("🚀 启动数据库管理工具...")
    
    # 检查数据库文件是否存在
    db_path = "books.db"
    if not os.path.exists(db_path):
        print(f"⚠️ 数据库文件 {db_path} 不存在，将创建新的数据库")
    
    tools = DatabaseTools(db_path)
    
    while True:
        show_menu()
        
        try:
            choice = input("\n请选择操作 (1-8): ").strip()

            if choice == '1':
                tools.show_statistics()
            elif choice == '2':
                tools.list_all_books()
            elif choice == '3':
                query = input("请输入搜索关键词: ").strip()
                if query:
                    tools.search_books(query)
                else:
                    print("⚠️ 搜索关键词不能为空")
            elif choice == '4':
                tools.add_sample_book()
            elif choice == '5':
                limit = input("显示数量 (默认10): ").strip()
                limit = int(limit) if limit.isdigit() else 10
                tools.show_recent_books(limit)
            elif choice == '6':
                filename = input("导出文件名 (默认books_export.json): ").strip()
                filename = filename if filename else "books_export.json"
                tools.export_to_json(filename)
            elif choice == '7':
                filename = input("备份文件名 (留空自动生成): ").strip()
                filename = filename if filename else None
                tools.backup_database(filename)
            elif choice == '8':
                print("👋 再见！")
                break
            else:
                print("❌ 无效选择，请输入 1-8")
                
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，再见！")
            break
        except Exception as e:
            print(f"❌ 操作失败: {e}")
        
        input("\n按回车键继续...")


if __name__ == "__main__":
    main()
