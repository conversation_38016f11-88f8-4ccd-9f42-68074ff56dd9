# Reader - 阅读器应用程序

一个基于PySide6的现代化阅读器应用程序，采用清晰的架构设计和模块化结构。

## 项目结构

```
Reader/
├── main.py                 # 应用程序入口
├── test_app.py            # 测试脚本
├── config/                # 配置文件
│   ├── __init__.py
│   ├── settings.py        # 应用设置
│   └── styles.py         # 样式配置
├── core/                  # 核心业务逻辑
│   ├── __init__.py
│   ├── models/           # 数据模型
│   │   ├── __init__.py
│   │   └── book.py       # 书籍模型
│   └── services/         # 业务服务
│       ├── __init__.py
│       └── book_service.py # 书籍服务
├── ui/                    # UI相关
│   ├── __init__.py
│   ├── components/       # UI组件
│   │   ├── __init__.py
│   │   ├── nav_item.py   # 导航项组件
│   │   └── book_widget.py # 书籍组件
│   ├── pages/            # 页面
│   │   ├── __init__.py
│   │   ├── main_window.py # 主窗口
│   │   └── bookshelf_page.py # 书架页面
│   └── utils/            # UI工具函数
│       ├── __init__.py
│       └── animations.py # 动画工具
└── resources/            # 资源文件
    ├── icons/
    └── styles/
```

## 架构特点

### 1. 分层架构
- **UI层**: 负责用户界面展示和交互
- **业务层**: 处理核心业务逻辑
- **数据层**: 数据模型和服务

### 2. 模块化设计
- **配置管理**: 统一的配置和样式管理
- **组件化UI**: 可复用的UI组件
- **服务导向**: 独立的业务服务

### 3. 清晰的依赖关系
- UI层依赖业务层
- 业务层独立于UI层
- 配置层被各层共享

## 主要功能

- 🏠 主页：欢迎页面和应用概览
- 📚 书架管理：浏览和管理书籍
- 🔍 搜索功能：快速查找书籍
- ⚙️ 设置页面：应用程序配置和偏好设置
- 🎨 现代化UI：响应式设计和动画效果
- 🖼️ 图标导航：直观的图标导航栏
- 📱 可扩展导航：支持展开/收缩的侧边栏

## 运行应用程序

```bash
python main.py
```

## 运行测试

```bash
python test_app.py
```

## 技术栈

- **GUI框架**: PySide6
- **语言**: Python 3.8+
- **架构模式**: MVC/分层架构

## 开发说明

### 添加新功能
1. 在 `core/models/` 中定义数据模型
2. 在 `core/services/` 中实现业务逻辑
3. 在 `ui/components/` 或 `ui/pages/` 中创建UI组件
4. 在 `config/` 中添加相关配置

### 样式管理
所有样式配置都在 `config/styles.py` 中统一管理，便于维护和主题切换。

### 信号和槽
使用PySide6的Signal/Slot机制实现组件间通信，保持松耦合。
